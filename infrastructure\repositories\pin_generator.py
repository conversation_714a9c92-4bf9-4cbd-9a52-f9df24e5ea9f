"""
PIN Generator Utility

Generates unique 6-digit PINs by checking against existing PINs in multiple database tables:
- box_sections (column: pin)
- sale_reservations (column: reservation_pin, only status=1 records)
- storage_reservations (column: reservation_pin, only status=1 records)
- order_reservations (column: pickup_pin, only status=1 records)
"""

import random
from managers.logger_manager import logger
import mysql.connector
from typing import Optional
from os import getenv
from managers.timeline_logger import log_timeline_event
feature_storage = getenv("FEATURE_STORAGE")
feature_sale = getenv("FEATURE_SALE")
feature_order = getenv("FEATURE_ORDER")

def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

def generate_pin(max_attempts: int = 100) -> Optional[str]:
    """
    Generate a unique 6-digit PIN that doesn't exist in any of the checked tables.

    Args:
        max_attempts: Maximum number of attempts to generate a unique PIN

    Returns:
        str: 6-digit PIN if successful
        None: If unable to generate unique PIN after max_attempts
    """

    for attempt in range(max_attempts):
        # Generate 6-digit numeric PIN
        pin = str(random.randint(100000, 999999))

        if _is_pin_unique(pin):
            return pin

        logger.info(f"PIN {pin} already exists, trying again (attempt {attempt + 1})")

    logger.error(f"Failed to generate unique PIN after {max_attempts} attempts")
    log_timeline_event(
        event_type="pin_generation",
        event_result="failed",
        entered_pin=pin,
    )
    return None

def _is_pin_unique(pin: str) -> bool:
    """
    Check if PIN is unique across all relevant database tables.

    Args:
        pin: 6-digit PIN to check

    Returns:
        bool: True if PIN is unique, False if it already exists
    """

    db = _get_db_connection()
    cursor = db.cursor()

    try:
        # Check box_sections table (column: pin)
        cursor.execute("SELECT COUNT(*) FROM box_sections WHERE pin = %s", (pin,))
        if cursor.fetchone()[0] > 0:
            logger.info(f"PIN {pin} found in box_sections table")
            return False

        # Check sale_reservations table (column: reservation_pin, only status=1)
        if feature_sale:        
            cursor.execute("""
                SELECT COUNT(*) FROM sale_reservations
                WHERE reservation_pin = %s AND status = 1
            """, (pin,))
            if cursor.fetchone()[0] > 0:
                logger.info(f"PIN {pin} found in sale_reservations table (status=1)")
                return False

        # Check storage_reservations table (column: reservation_pin, only status=1)
        if feature_storage:
            cursor.execute("""
                SELECT COUNT(*) FROM storage_reservations
                WHERE reservation_pin = %s AND status = 1
            """, (pin,))
            if cursor.fetchone()[0] > 0:
                logger.info(f"PIN {pin} found in storage_reservations table (status=1)")
                return False

        # Check order_reservations table (column: pickup_pin, only status=1)
        if feature_order:
            cursor.execute("""
                SELECT COUNT(*) FROM order_reservations
                WHERE pickup_pin = %s AND status = 1
            """, (pin,))
            if cursor.fetchone()[0] > 0:
                logger.info(f"PIN {pin} found in order_reservations table (status=1)")
                return False

        # PIN is unique across all tables
        return True

    except Exception as e:
        logger.error(f"Error checking PIN uniqueness: {e}")
        return False

    finally:
        cursor.close()
        db.close()

def validate_pin_format(pin: str) -> bool:
    """
    Validate that PIN is exactly 6 digits.

    Args:
        pin: PIN to validate

    Returns:
        bool: True if PIN format is valid, False otherwise
    """
    return pin.isdigit() and len(pin) == 6
