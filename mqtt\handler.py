import uuid
import async<PERSON>
from typing import Dict, Any, List
import threading
from managers.timeline_logger import log_timeline_event
from managers.logger_manager import logger

class MQTTCommandHandler:
    """Handler for processing MQTT command messages"""

    def __init__(self):
        pass

    async def handle_command(self, topic_parts: List[str], payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming MQTT command and return response

        Args:
            topic_parts: List of topic parts (split by '/')
            payload: Parsed JSON payload from the message

        Returns:
            Response dictionary to be published
        """
        try:
            command_type = topic_parts[3]  # electronic/system/sale/storage
            command_action = topic_parts[4] if len(topic_parts) > 4 else None

            # Validate request_uuid is present
            request_uuid = payload.get("request_uuid")
            if not request_uuid:
                response = {
                    "success": False,
                    "message": "Missing required field 'request_uuid'",
                    "request_uuid": str(uuid.uuid4())  # Generate one for the error response
                }
                return response

            # Default response
            response = {"success": True, "request_uuid": request_uuid}

            match command_type:
                # Handle ELECTRONIC commands
                case "electronic":
                    response = self.handle_electronic_command(command_action, payload, response)

                # Handle SYSTEM commands
                case "system":
                    response = self.handle_system_command(command_action, payload, response)

                # Handle SALE commands
                case "sale":
                    response = self.handle_sale_command(command_action, payload, response)

                # Handle storage commands
                case "storage":
                    response = await self.handle_storage_command(command_action, payload, response)

            return response

        except Exception as e:
            # Return error response for any exceptions
            return {
                "success": False,
                "message": f"Error processing command: {str(e)}",
                "request_uuid": payload.get("request_uuid", str(uuid.uuid4()))
            }


    def handle_electronic_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle ELECTRONIC commands"""
        from managers.sequence_manager import sequence_manager
        from hardware.locker_control import LockerController
        from managers.session_manager import SessionType, SectionConfig
        import mysql.connector
        from os import getenv

        locker_controller = LockerController()
        section_id = payload.get("section_id", 1)
        response["section_id"] = section_id

        # Get section information from database
        from infrastructure.repositories.section_repository import section_repository
        section_info = section_repository.get_section_data(str(section_id))
        if not section_info or not section_info.get("visible", 0):
            response.update({
                "success": False,
                "message": f"Section {section_id} not found in database"
            })
            return response

        is_tempered = bool(section_info["tempered"])

        match command_action:

            case "unlock_service":
                # Open service door with unlock_service() function
                try:
                    success = asyncio.run(locker_controller.unlock_service())
                    if success:
                        response["message"] = "Service door unlocked successfully"
                    else:
                        response.update({
                            "success": False,
                            "message": "Failed to unlock service door"
                        })
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error unlocking service door: {str(e)}"
                    })

                # Remove section_id from response for unlock_service
                if "section_id" in response:
                    del response["section_id"]

                return response

            case "check_service":
                # Check service door state with check_service() function
                try:
                    door_state = asyncio.run(locker_controller.check_service())
                    if door_state is True:
                        response["message"] = "Service door is opened"
                        response["door_state"] = "opened"
                    elif door_state is False:
                        response["message"] = "Service door is closed"
                        response["door_state"] = "closed"
                    else:
                        response["message"] = "Failed to check service door state"
                        response["door_state"] = "unknown"
                        response["success"] = False
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error checking service door state: {str(e)}",
                        "door_state": "unknown"
                    })

                # Remove section_id from response for check_service
                if "section_id" in response:
                    del response["section_id"]

                return response


            case "unlock":
                # Open section with unlock_locker() function
                try:
                    success = asyncio.run(locker_controller.unlock_locker(section_id))
                    if success:
                        response["message"] = f"Section {section_id} unlocked successfully"
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to unlock section {section_id}"
                        })
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error unlocking section {section_id}: {str(e)}"
                    })

                return response

            case "section_open":
                # Open section using start_fsm_sequence directly (simpler approach for MQTT)
                try:
                    from managers.sequence_manager import sequence_manager
                    from managers.session_manager import SectionConfig

                    # Check if there are any active FSM sequences running
                    active_sequences = sequence_manager.get_active_sequences()
                    if active_sequences:
                        response.update({
                            "success": False,
                            "message": f"Cannot start section {section_id} opening - FSM sequence already active: {', '.join(active_sequences)}"
                        })
                        return response

                    # Create a session for the sequence
                    session_id = f"mqtt_open_{section_id}_{payload.get('request_uuid', 'unknown')[:8]}"

                    # Create section config
                    section_config = SectionConfig(
                        section_id=section_id,
                        lock_id=section_id,  # Default to same as section_id
                        is_tempered=is_tempered,  # Use the is_tempered from section info
                        led_section=section_id  # Set LED section to same as section_id
                    )

                    # Run the FSM sequence in a background thread to avoid event loop issues
                    def run_fsm_in_background():
                        loop = None
                        try:
                            # Create a new persistent event loop for this background task
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)

                            # Start FSM sequence directly
                            success = loop.run_until_complete(
                                sequence_manager.start_fsm_sequence(
                                    session_id=session_id,
                                    sections=[section_config],
                                    pin=None,
                                    operator_id="mqtt"
                                )
                            )

                            if success:
                                logger.info(f"MQTT FSM sequence started successfully for section {section_id}")
                                # Keep the loop running until the sequence completes
                                if session_id in sequence_manager.active_sequences:
                                    try:
                                        loop.run_until_complete(sequence_manager.active_sequences[session_id])
                                        logger.info(f"MQTT FSM sequence completed successfully for section {section_id}")
                                    except asyncio.CancelledError:
                                        logger.info(f"MQTT FSM sequence cancelled for section {section_id}")
                                    except Exception as e:
                                        logger.error(f"MQTT FSM sequence failed during execution: {e}")
                            else:
                                logger.error(f"Failed to start MQTT FSM sequence for section {section_id}")

                        except Exception as e:
                            logger.error(f"Error in background FSM sequence for section {section_id}: {e}")
                        finally:
                            # Properly close the loop and handle any pending tasks
                            if loop is not None:
                                try:
                                    # Cancel all pending tasks
                                    pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
                                    if pending_tasks:
                                        logger.info(f"Cancelling {len(pending_tasks)} pending tasks for section {section_id}")
                                        for task in pending_tasks:
                                            task.cancel()

                                        # Wait for tasks to be cancelled
                                        if pending_tasks:
                                            loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))

                                    # Close the loop
                                    loop.close()
                                except Exception as e:
                                    logger.error(f"Error closing event loop for section {section_id}: {e}")

                    # Start the sequence in a daemon thread so it doesn't block the MQTT response
                    sequence_thread = threading.Thread(target=run_fsm_in_background, daemon=True)
                    sequence_thread.start()

                    # Return immediate success response since we started the sequence
                    response["message"] = f"Section {section_id} opening sequence started successfully"
                    response["session_id"] = session_id

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error starting opening sequence for section {section_id}: {str(e)}"
                    })

                return response

            case "door_state":
                # Check door state with check_door_state()
                try:
                    door_state = asyncio.run(locker_controller.check_door_state(section_id, is_tempered=is_tempered))
                    if door_state is True:
                        response["message"] = f"Door state checked successfully"
                        response["door_state"] = "open"
                    elif door_state is False:
                        response["message"] = f"Door state checked successfully"
                        response["door_state"] = "closed"
                    else:
                        response["message"] = f"Door state check failed for section {section_id}"
                        response["door_state"] = "unknown"
                        response["success"] = False
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error checking door state for section {section_id}: {str(e)}"
                    })

                return response

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown electronic command: {command_action}",
                })

        return response







    def handle_sale_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle SALE commands"""
        from infrastructure.repositories.product_repository import product_repository

        match command_action:
            case "edit_reservation":
                # Enhanced edit_reservation with support for price updates
                try:
                    section_id = payload.get("section_id")
                    product_uuid = payload.get("product_uuid")
                    price = payload.get("price")  # New parameter for price editing
                    status_to_set = payload.get("status")  # New parameter for status editing

                    if not product_uuid and not section_id:
                        response.update({
                            "success": False,
                            "message": "Either 'product_uuid' or 'section_id' must be provided"
                        })
                        return response

                    if status_to_set == 0:
                        action = 2  # Remove action
                    else:
                        action = 1  # Insert action


                    result = asyncio.run(product_repository.edit_reservation(
                        section_id=section_id if section_id else None,
                        reservation_uuid=product_uuid if product_uuid else None,
                        price=price if price is not None else None,
                        status_to_set=status_to_set if status_to_set is not None else None,
                        action=action
                    ))

                    if result:
                        response["message"] = "Product reservation updated successfully"
                        if price is not None:
                            response["price"] = price
                        response["product_uuid"] = result.get("uuid")
                        response["section_id"] = int(result.get("section_id")) if result.get("section_id") else None
                        response["status"] = result.get("status")  # Always return current status

                        log_timeline_event(
                            event_type="product_edit",
                            event_result="success",
                            message=f"Product reservation updated successfully for section {section_id}, status: {status_to_set}, price: {price}",
                            section_id=section_id,
                            mode="product",
                            operator_id="distribox_web"
                        )

                    else:
                        response.update({
                            "success": False,
                            "message": "Failed to update product reservation"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error editing reservation: {str(e)}"
                    })

            case "reserve_product":
                # Use reserve_section to reserve a product
                try:
                    section_id = payload.get("section_id")
                    reservation_pin = payload.get("reservation_pin")
                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not section_id:
                        response.update({
                            "success": False,
                            "message": "'section_id' must be provided"
                        })
                        return response

                    # Use reserve_section from product service
                    from domains.product.service import product_service
                    result = asyncio.run(product_service.reserve_section(
                        section_id=section_id,
                        reservation_pin=reservation_pin
                    ))

                    if result.get("success"):
                        response["message"] = result.get("message", "Product reserved successfully")
                        response["section_id"] = int(section_id) if section_id else None
                        response["reservation_pin"] = str(result.get("reservation_pin")) if result.get("reservation_pin") else None
                        product_data = result.get("product", {})
                        response["product_uuid"] = product_data.get("uuid")
                        response["price"] = product_data.get("price")
                        response["status"] = product_data.get("status")  # Always return current status
                    else:
                        response.update({
                            "success": False,
                            "message": result.get("message", f"Failed to reserve product in section {section_id}"),
                            "error_code": result.get("error_code")
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error reserving product: {str(e)}"
                    })

            case "unreserve_product":
                # Use cancel_reservation to unreserve a product
                try:
                    section_id = payload.get("section_id")
                    reservation_pin = payload.get("reservation_pin")
                    product_uuid = payload.get("product_uuid")

                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not section_id and not reservation_pin and not product_uuid:
                        response.update({
                            "success": False,
                            "message": "Either 'section_id', 'reservation_pin', or 'product_uuid' must be provided"
                        })
                        return response

                    # Find the reserved product using a single call with conditional parameters
                    reserved_products = product_repository.find_reservations(
                        section_id=section_id if section_id else None,
                        reservation_pin=reservation_pin if reservation_pin else None,
                        reservation_uuid=product_uuid if product_uuid else None,
                        status=1,
                        reserved=1,
                        limit=1
                    )

                    if not reserved_products:
                        # Build error message based on what was provided
                        if reservation_pin:
                            error_msg = f"No reserved product found with PIN '{reservation_pin}'"
                        elif product_uuid:
                            error_msg = f"No reserved product found with UUID '{product_uuid}'"
                        else:
                            error_msg = f"No reserved product found in section {section_id}"

                        response.update({
                            "success": False,
                            "message": error_msg
                        })
                        return response

                    cancelled_pin = reserved_products[0].get('reservation_pin')
                    product_uuid = reserved_products[0].get('uuid')
                    section_id = reserved_products[0].get('section_id')  # Get section_id from found product

                    # Use edit_reservation to unreserve the product
                    result = asyncio.run(product_repository.edit_reservation(
                        section_id=section_id,
                        reserved=0,
                        reservation_pin_new="",  # Use empty string instead of None
                        action=3  # Action code 2 for unreserve operations
                    ))

                    if result:
                        response["message"] = "Product unreserved successfully"
                        response["section_id"] = int(section_id) if section_id else None
                        response["cancelled_pin"] = str(cancelled_pin) if cancelled_pin else None
                        response["product_uuid"] = product_uuid
                        response["status"] = result.get("status")  # Always return current status
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to unreserve product in section {section_id}"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error unreserving product: {str(e)}"
                    })

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown sale command: {command_action}",
                })

        return response





    async def handle_storage_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle STORAGE commands"""
        from infrastructure.repositories.storage_repository import storage_repository

        match command_action:
            case "edit_reservation":
                # Use deactivate_reservation to mark storage reservation as completed
                try:
                    section_id = payload.get("section_id")
                    reservation_uuid = payload.get("reservation_uuid")
                    reservation_pin = payload.get("reservation_pin")

                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not reservation_uuid and not section_id and not reservation_pin:
                        response.update({
                            "success": False,
                            "message": "Either 'reservation_uuid', 'section_id', or 'reservation_pin' must be provided"
                        })
                        return response

                    # Find the reservation first to get complete information
                    reservation_info = None
                    if reservation_uuid:
                        # Find reservation by UUID
                        reservations = storage_repository.find_reservations(reservation_uuid=reservation_uuid, status=1, limit=1)
                        reservation_info = reservations[0] if reservations else None
                    elif reservation_pin:
                        # Find reservation by PIN
                        reservations = storage_repository.find_reservations(reservation_pin=reservation_pin, status=1, limit=1)
                        reservation_info = reservations[0] if reservations else None
                    elif section_id:
                        # Find reservation by section_id
                        # We need to get the active reservation for this section first
                        import mysql.connector
                        from os import getenv
                        db = mysql.connector.connect(
                            host=getenv("DB_HOST"),
                            port=int(getenv("DB_PORT")),
                            database=getenv("DB_NAME"),
                            user=getenv("DB_USER"),
                            password=getenv("DB_PASSWORD")
                        )
                        cursor = db.cursor(dictionary=True)
                        try:
                            cursor.execute("""
                                SELECT * FROM storage_reservations
                                WHERE section_id = %s AND status = 1
                                LIMIT 1
                            """, (str(section_id),))
                            reservation_info = cursor.fetchone()
                        finally:
                            cursor.close()
                            db.close()

                    if not reservation_info:
                        response.update({
                            "success": False,
                            "message": "Storage reservation not found"
                        })
                        return response

                    # Now deactivate the reservation using edit_reservations
                    updated_reservations = await storage_repository.edit_reservations(
                        reservation_uuid=reservation_info.get("uuid"),
                        status_to_set=0,  # Deactivate
                        action=0  # purchased/completed
                    )

                    if updated_reservations:
                        response["message"] = "Storage edited successfully"
                        response["request_uuid"] = reservation_info.get("uuid", "")
                        response["reservation_uuid"] = reservation_info.get("uuid", "")
                        response["section_id"] = int(reservation_info.get("section_id", 0))
                        response["status"] = 0  # Status after deactivation
                        response["reservation_pin"] = str(reservation_info.get("reservation_pin", ""))
                    else:
                        response.update({
                            "success": False,
                            "message": "Failed to edit storage reservation"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error editing storage reservation: {str(e)}"
                    })

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown storage command: {command_action}",
                })

        return response





    def handle_system_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle SYSTEM commands"""
        match command_action:
            case "reboot_device":
                # TODO: Reboot device
                response["message"] = "Rebooting"
                return response
            case _:
                response.update({
                    "success": False,
                    "message": "Unknown command",
                })
                return response




# Global handler instance
command_handler = MQTTCommandHandler()
