-- migrate:up
CREATE TABLE timeline_api_log (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    serial_number VARCHAR(255) DEFAULT NULL,
    endpoint VARCHAR(255) DEFAULT NULL,
    method VARCHAR(20) DEFAULT NULL,
    payload LONGTEXT DEFAULT NULL,
    status INT(11) DEFAULT NULL,
    response LONGTEXT DEFAULT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE timeline_api_log;
