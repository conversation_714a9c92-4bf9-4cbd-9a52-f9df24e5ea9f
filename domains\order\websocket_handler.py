"""
WebSocket handler for order operations.
Uses universal pickup_process() and select_sections() functions for all operations.
"""

import json
import asyncio
import random
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from managers.logger_manager import logger
# OrderRepository import removed - reservations now created via /order/create_reservation endpoint
from infrastructure.repositories.section_repository import section_repository
from infrastructure.external_apis.jetveo_client import jetveo_client



async def handle_websocket_messages(websocket: WebSocket, session_id: str, message_queue: asyncio.Queue, operation_name: str = "order"):
    """
    Universal WebSocket message handler that routes messages to the provided message queue.

    Args:
        websocket: WebSocket connection
        session_id: Session identifier
        message_queue: Queue to route messages to
        operation_name: Name of the operation for logging purposes
    """
    while ws_manager.is_connected(session_id):
        try:
            message = await websocket.receive_text()
            if not message or message.strip() == "":
                continue

            try:
                data = json.loads(message)
            except json.JSONDecodeError:
                await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                continue

            msg_type = data.get("type")

            if msg_type == "ping":
                await ws_manager.send(session_id, {"type": "pong"})
                continue

            # Route message to the appropriate handler via message queue
            await message_queue.put(data)

        except WebSocketDisconnect:
            logger.info(f"{operation_name} WebSocket disconnected: {session_id}")
            break
        except Exception as e:
            logger.error(f"Error processing WebSocket message in {operation_name}: {e}")
            break


async def handle_order_websocket(websocket: WebSocket, session_id: str):
    """
    Universal WebSocket handler for all order operations.
    Routes to pickup_process() or select_sections() based on operation type.
    """
    logger.info(f"Order WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Order WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        # Get operation details from session
        operation = getattr(session, 'operation', 'unknown')
        logger.info(f"Order operation: {operation}")

        # Create message queue for WebSocket communication
        message_queue = asyncio.Queue()

        # Start universal message handler task
        message_task = asyncio.create_task(handle_websocket_messages(websocket, session_id, message_queue, operation))

        # Route to appropriate handler based on operation type
        if operation in ["pickup_expired", "pickup_employee", "customer_pickup", "check_after_delivery_pickup"]:
            await _handle_pickup_operation(session_id, session, operation, message_queue)
        elif operation == "operator_deliver":
            await _handle_operator_deliver_operation(session_id, session, message_queue)
        elif operation == "customer_send_orders":
            await _handle_customer_send_operation(session_id, session, message_queue)
        elif operation == "employee_send":
            await _handle_employee_send(session_id, session, message_queue)
        elif operation == "deliver_employee":
            await _handle_employee_deliver(session_id, session, message_queue)
        elif operation == "customer_send":
            await _handle_employee_send(session_id, session, message_queue)
        elif operation == "deliver_order":
            await _handle_employee_deliver(session_id, session, message_queue)
        else:
            logger.error(f"Unknown operation: {operation}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Unknown operation: {operation}"
            })

        # Cancel message handler task
        message_task.cancel()

    except Exception as e:
        logger.error(f"Error in order WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Order WebSocket handler ended for session: {session_id}")


async def _handle_pickup_operation(session_id: str, session, operation: str, message_queue: asyncio.Queue):
    """Handle pickup operations using pickup_process()"""
    logger.info(f"Handling pickup operation: {operation}")


    # Get sections and operation details from session
    sections = getattr(session, 'sections', None)  # This field exists in SessionData
    endpoint_type = getattr(session, 'endpoint_type', 'unknown')

    if not sections:
        logger.error(f"No sections found in session {session_id}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": "No sections found"
        })
        return



    # AGE CHECK STEP
    if endpoint_type == "order/customer/pickup":
        age_control_required =  getattr(session, 'age_control_required', False)
        age_controlled = getattr(session, 'age_controlled', False)
        if age_control_required and not age_controlled:
            from managers.process_manager import age_check_process
            success = await age_check_process(session_id, message_queue)
            if not success:
                return
            session_manager.update_session(session_id, age_controlled=True)

            # Edit reservation
            from infrastructure.repositories.order_repository import order_repository
            await order_repository.edit_reservations(
                order_number=getattr(session, 'order_number', None),
                age_controlled=1
            )

    # PAYMENT STEP
    if endpoint_type == "order/customer/pickup":
        amount = getattr(session, 'amount', 0)
        if amount > 0:
            from transaction.manager import transaction_manager
            payment_success = await transaction_manager.make_payment(
                session_id=session_id,
                amount=amount,
                message_queue=message_queue,
                mode="order"
            )
            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                return
            # Mark payment as completed
            await order_repository.edit_reservations(
                order_number=getattr(session, 'order_number', None),
                paid_status=1
            )



    # SECTION OPENING STEP
    from managers.process_manager import pickup_process
    success, _ = await pickup_process(
        sections=sections,
        session_id=session_id,
        message_queue=message_queue
    )

    # reservation is deactivated after section opening
    # order_change_status is send after section opening 
    logger.info(f"Order pickup completed for session {session_id}: success={success}")



async def _handle_employee_send(session_id: str, session, message_queue: asyncio.Queue):
    """Handle employee send operation using select_sections()"""
    logger.info(f"Handling employee send operation")

    # Get operation details from session
    phone_number = getattr(session, 'phone_number', None)
    reserved_section_ids = None     # TODO
    all_available_sections = section_repository.get_available_sections("order")

    logger.info(f"Operation: employee_send, reserved_section_ids: {reserved_section_ids}")

    # Determine available sections for employee_send
    if reserved_section_ids:
        # Multiple section operations - use reserved sections
        reserved_sections = reserved_section_ids
    else:
        # Get all available sections (fallback)
        reserved_sections = all_available_sections

    logger.info(f"Final sections - available sections: {all_available_sections}, reserved: {reserved_sections}")


    # Start select_sections from universal process_manager
    from managers.process_manager import select_sections
    success, selected_sections = await select_sections(
        reserved_sections=reserved_sections,
        available_sections=all_available_sections,
        session_id=session_id,
        message_queue=message_queue
    )

    logger.info(f"Employee send selection completed for session {session_id}: success={success}, selected_sections={selected_sections}")

    # Handle WebSocket disconnection after select_sections completes
    if success:
        logger.info(f"Cleaning up WebSocket session for employee send: {session_id}")
        ws_manager.disconnect(session_id)
        await session_manager.remove_session(session_id)

    # Create reservations based on selected_sections (similar to /create-reservation endpoint)
    if success and selected_sections:
        from infrastructure.repositories.order_repository import order_repository

        # Get session details
        phone_number = getattr(session, 'phone_number', None)

        logger.info(f"Creating reservations for employee send - phone: {phone_number}, sections: {selected_sections}")

        # Create reservations for employee send (status=8, similar to "order/employment/customer/send")
        created_reservations = []
        for section in selected_sections:
            result = await order_repository.create_order_reservation(
                phone_number=phone_number,
                section_id=section,
                status=9,       # for courier to pickup
                auto_generate_pin=True,
                type="employee_send"
            )
            if result.get('success'):
                created_reservations.append({
                    'section_id': section,
                    'reservation_id': result.get('reservation_id'),
                    'insert_pin': result.get('insert_pin')
                })
                logger.info(f"Created employee send reservation: section={section}, insert_pin={result.get('insert_pin')}")

        logger.info(f"Created {len(created_reservations)} reservations for employee send")
    else:
        logger.info(f"Employee send selection failed for session {session_id}: success={success}")

    logger.info(f"Employee send selection completed for session {session_id}: success={success}")


async def _handle_employee_deliver(session_id: str, session, message_queue: asyncio.Queue):
    """Handle employee deliver operation using select_sections()"""
    logger.info(f"Handling employee deliver operation")

    # Get operation details from session
    phone_number = getattr(session, 'phone_number', None)
    reserved_sections = getattr(session, 'reserved_sections', None)
    all_available_sections = section_repository.get_available_sections("order")

    logger.info(f"Operation: deliver_employee, reserved_sections: {reserved_sections}")

    # Determine available sections for deliver_employee
    if not reserved_sections:
        from infrastructure.repositories.order_repository import order_repository
        reserved_sections = [order_repository.select_larger_section()]


    logger.info(f"Final sections - available: {all_available_sections}, reserved: {reserved_sections}")


    # Start select_sections from universal process_manager
    from managers.process_manager import select_sections
    success, selected_sections = await select_sections(
        reserved_sections=reserved_sections,
        available_sections=all_available_sections,
        session_id=session_id,
        message_queue=message_queue
    )


    logger.info(f"Employee deliver selection completed for session {session_id}: success={success}, selected_sections={selected_sections}")
    logger.info(f"Cleaning up WebSocket session for employee deliver: {session_id}")
    ws_manager.disconnect(session_id)
    await session_manager.remove_session(session_id)

    # Create reservations based on selected_sections (similar to /create-reservation endpoint)
    if success and selected_sections:
        from infrastructure.repositories.order_repository import order_repository
        from infrastructure.repositories.pin_generator import generate_pin

        # Get session details
        phone_number = getattr(session, 'phone_number', None)

        logger.info(f"Creating reservations for employee deliver - phone: {phone_number}, sections: {selected_sections}")

        # Create reservations for employee deliver (status=1 with pickup_pin, similar to "order/employment/operator/deliver")
        new_pickup_pin = generate_pin()
        created_reservations = []
        for section in selected_sections:
            result = await order_repository.create_order_reservation(
                phone_number=phone_number,
                section_id=section,
                status=1,
                pickup_pin=new_pickup_pin,
                auto_generate_pin=False,  # We're providing pickup_pin, don't auto-generate insert_pin
                type="deliver_to_employee"
            )
            if result.get('success'):
                created_reservations.append({
                    'section_id': section,
                    'reservation_id': result.get('reservation_id'),
                    'pickup_pin': result.get('pickup_pin')
                })
                logger.info(f"Created employee deliver reservation: section={section}, pickup_pin={result.get('pickup_pin')}")

        logger.info(f"Created {len(created_reservations)} reservations for employee deliver with pickup_pin: {new_pickup_pin}")
    else:
        logger.info(f"Employee deliver selection failed for session {session_id}: success={success}")

    logger.info(f"Employee deliver selection completed for session {session_id}: success={success}")


async def _handle_operator_deliver_operation(session_id: str, session, message_queue: asyncio.Queue):
    """Handle operator deliver operation - wait for order scans and process them"""
    logger.info(f"Handling operator deliver operation for session {session_id}")

    # Import order repository
    from infrastructure.repositories.order_repository import order_repository

    # Send initial waiting_for_order message
    await ws_manager.send(session_id, {
        "type": "waiting_for_order",
        "message": "Waiting for order scan"
    })

    # Main processing loop
    try:
        while ws_manager.is_connected(session_id):
            
            await ws_manager.send(session_id, {
                "type": "waiting_for_order",
                "message": "Waiting for next order scan"
            })
            
            try:
                # Wait for message from websocket
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message: {e}")
                break

            message_type = message.get("type")
            logger.info(f"Processing operator deliver message: {message_type}")

            if message_type == "order_scanned":
                package_pin = message.get("package_pin")
                insert_pin = message.get("insert_pin")

                if package_pin:
                    # Handle package_pin scan
                    logger.info(f"Package PIN scanned: {package_pin}")

                    # Find reservation with this package_pin and status 7 or 8, there is always only one
                    valid_reservation = await order_repository.find_reservations(package_pin=package_pin, statuses=[7, 8])[0]

                    if not valid_reservation:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid order found for package PIN: {package_pin}"
                        })
                        continue

                    # Get the first valid reservation
                    reservation = valid_reservation[0]
                    section_id = reservation.get('section_id')

                    if not section_id:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "No section assigned to this order"
                        })
                        continue

                    # Call select_sections for this specific section
                    from managers.process_manager import select_sections
                    success, selected_sections = await select_sections(
                        reserved_sections=[section_id],
                        available_sections=[section_id],
                        session_id=session_id,
                        message_queue=message_queue,
                        package_pin=package_pin,
                        insert_pin=insert_pin
                    )

                    logger.info(f"Operator deliver section selection completed for package {package_pin}: success={success}, selected_sections={selected_sections}")

                    if success and selected_sections:
                        # Change status to 3
                        await order_repository.edit_reservations(
                            package_pin=package_pin,
                            find_status=reservation.get('status'),
                            set_status=3
                        )

                        await ws_manager.send(session_id, {
                            "type": "order_processed",
                            "message": f"Order with package PIN {package_pin} processed successfully",
                            "package_pin": package_pin,
                            "section_id": section_id
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Section selection failed"
                        })

                elif insert_pin:
                    # Handle insert_pin scan
                    logger.info(f"Insert PIN scanned: {insert_pin}")

                    # Find reservations with this insert_pin and status 7 or 8
                    valid_reservation = await order_repository.find_reservations(insert_pin=insert_pin, statuses=[7, 8])[0]

                    if not valid_reservation:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid order found for package PIN: {package_pin}"
                        })
                        continue

                    # Get the first valid reservation
                    reservation = valid_reservation[0]
                    section_id = reservation.get('section_id')

                    if not section_id:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "No section assigned to this order"
                        })
                        continue

                    # Call select_sections for this specific section
                    from managers.process_manager import select_sections
                    success, selected_sections = await select_sections(
                        reserved_sections=[section_id],
                        available_sections=[section_id],
                        session_id=session_id,
                        message_queue=message_queue,
                        package_pin=package_pin,
                        insert_pin=insert_pin
                    )

                    logger.info(f"Operator deliver section selection completed for package {package_pin}: success={success}, selected_sections={selected_sections}")

                    if success and selected_sections:
                        # Change status to 3
                        await order_repository.edit_reservations(
                            package_pin=package_pin,
                            find_status=reservation.get('status'),
                            set_status=3
                        )

                        await ws_manager.send(session_id, {
                            "type": "order_processed",
                            "message": f"Order with package PIN {package_pin} processed successfully",
                            "package_pin": package_pin,
                            "section_id": section_id
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Section selection failed"
                        })

                else:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Either package_pin or insert_pin must be provided"
                    })

            # End deliver operation
            elif message_type == "end_deliver":
                await ws_manager.send(session_id, {"type": "delivery_completed"})
                logger.info(f"Cleaning up WebSocket session for operator deliver: {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)


    except Exception as e:
        logger.error(f"Error in operator deliver handler: {e}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"Internal error: {str(e)}"
        })
    finally:
        logger.info(f"Operator deliver operation completed for session {session_id}")


async def _handle_customer_send_operation(session_id: str, session, message_queue: asyncio.Queue):
    """Handle customer send operation - wait for order scans and process them (similar to operator deliver but for status 8)"""
    logger.info(f"Handling customer send operation for session {session_id}")

    # Import order repository
    from infrastructure.repositories.order_repository import order_repository

    # Send initial waiting_for_order message
    await ws_manager.send(session_id, {
        "type": "waiting_for_order",
        "message": "Waiting for order scan"
    })

    # Main processing loop
    try:
        while ws_manager.is_connected(session_id):

            await ws_manager.send(session_id, {
                "type": "waiting_for_order",
                "message": "Waiting for next order scan"
            })

            try:
                # Wait for message from websocket
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message: {e}")
                break

            message_type = message.get("type")
            logger.info(f"Processing customer send message: {message_type}")

            if message_type == "order_scanned":
                package_pin = message.get("package_pin")
                insert_pin = message.get("insert_pin")

                if package_pin:
                    # Handle package_pin scan
                    logger.info(f"Package PIN scanned: {package_pin}")

                    # Find reservation with this package_pin and status 8, there is always only one
                    valid_reservations = await order_repository.find_reservations(package_pin=package_pin, statuses=[8])

                    if not valid_reservations:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid order found for package PIN: {package_pin}"
                        })
                        continue

                    # Get the first valid reservation
                    reservation = valid_reservations[0]
                    section_id = reservation.get('section_id')

                    if not section_id:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "No section assigned to this order"
                        })
                        continue

                    # Call select_sections for this specific section
                    from managers.process_manager import select_sections
                    success, selected_sections = await select_sections(
                        reserved_sections=[section_id],
                        available_sections=[section_id],
                        session_id=session_id,
                        message_queue=message_queue,
                        package_pin=package_pin,
                        insert_pin=insert_pin
                    )

                    logger.info(f"Customer send section selection completed for package {package_pin}: success={success}, selected_sections={selected_sections}")

                    # Handle WebSocket disconnection after select_sections completes
                    if success:
                        logger.info(f"Cleaning up WebSocket session for customer send: {session_id}")
                        ws_manager.disconnect(session_id)
                        await session_manager.remove_session(session_id)

                    if success and selected_sections:
                        # Change status to 3 (delivered/sent)
                        await order_repository.edit_reservations(
                            package_pin=package_pin,
                            find_status=reservation.get('status'),
                            set_status=3
                        )

                        await ws_manager.send(session_id, {
                            "type": "order_processed",
                            "message": f"Order with package PIN {package_pin} processed successfully",
                            "package_pin": package_pin,
                            "section_id": section_id
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Section selection failed"
                        })

                elif insert_pin:
                    # Handle insert_pin scan - find all reservations with this insert_pin and status 8
                    logger.info(f"Insert PIN scanned: {insert_pin}")

                    valid_reservations = await order_repository.find_reservations(insert_pin=insert_pin, statuses=[8])

                    if not valid_reservations:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid orders found for insert PIN: {insert_pin}"
                        })
                        continue

                    # Process all reservations with this insert_pin
                    processed_count = 0
                    for reservation in valid_reservations:
                        section_id = reservation.get('section_id')

                        if not section_id:
                            continue

                        # Call select_sections for this specific section
                        from managers.process_manager import select_sections
                        success, selected_sections = await select_sections(
                            reserved_sections=[section_id],
                            available_sections=[section_id],
                            session_id=session_id,
                            message_queue=message_queue,
                            package_pin=reservation.get('package_pin'),
                            insert_pin=insert_pin
                        )

                        logger.info(f"Customer send section selection completed for insert_pin {insert_pin}, section {section_id}: success={success}")

                        # Handle WebSocket disconnection after select_sections completes
                        if success:
                            logger.info(f"Cleaning up WebSocket session for customer send: {session_id}")
                            ws_manager.disconnect(session_id)
                            await session_manager.remove_session(session_id)

                        if success and selected_sections:
                            # Change status to 3 (delivered/sent)
                            await order_repository.edit_reservations(
                                reservation_id=reservation.get('id'),
                                set_status=3
                            )
                            processed_count += 1

                    if processed_count > 0:
                        # Call order_change_status for the order
                        order_number = valid_reservations[0].get('order_number')
                        order_uuid = valid_reservations[0].get('order_uuid')

                        if order_number and order_uuid:
                            from infrastructure.external_apis.jetveo_client import jetveo_client
                            await jetveo_client.order_change_status(status=3, order_number=order_number, order_uuid=order_uuid)

                        await ws_manager.send(session_id, {
                            "type": "orders_processed",
                            "message": f"Processed {processed_count} orders for insert PIN {insert_pin}",
                            "insert_pin": insert_pin,
                            "processed_count": processed_count
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "No orders could be processed"
                        })

                else:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "No package_pin or insert_pin provided"
                    })

            # End send operation
            elif message_type == "end_send":
                await ws_manager.send(session_id, {"type": "send_completed"})
                logger.info(f"Cleaning up WebSocket session for customer send: {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)
                break

    except Exception as e:
        logger.error(f"Error in customer send handler: {e}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"Internal error: {str(e)}"
        })
    finally:
        logger.info(f"Customer send operation completed for session {session_id}")

