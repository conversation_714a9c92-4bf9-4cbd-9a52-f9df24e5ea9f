from fastapi import APIRouter, HTTPException
from typing import Optional
from managers.logger_manager import logger
from datetime import datetime

from .models import TransactionCallback, PaymentCallback
from .manager import transaction_manager
from managers import session_manager, SessionType

router = APIRouter()

@router.post("/callback", response_model=TransactionCallback)
async def transaction_callback(callback: PaymentCallback) -> TransactionCallback:
    """
    Handle callback from payment terminal
    """
    logger.info(f"Received payment callback: {callback}")
    
    try:
        # Find active payment session (either TRANSACTION, PRODUCT_FLOW, PRODUCT_PICKUP, STORAGE_FLOW, or STORAGE_PICKUP)
        transaction_sessions = session_manager.get_sessions_by_type(SessionType.TRANSACTION)
        product_flow_sessions = session_manager.get_sessions_by_type(SessionType.PRODUCT_FLOW)
        product_pickup_sessions = session_manager.get_sessions_by_type(SessionType.PRODUCT_PICKUP)
        storage_flow_sessions = session_manager.get_sessions_by_type(SessionType.STORAGE_FLOW)
        storage_pickup_sessions = session_manager.get_sessions_by_type(SessionType.STORAGE_PICKUP)

        logger.info(f"Looking for active payment session - Transaction sessions: {len(transaction_sessions)}, Product flow sessions: {len(product_flow_sessions)}, Product pickup sessions: {len(product_pickup_sessions)}, Storage flow sessions: {len(storage_flow_sessions)}, Storage pickup sessions: {len(storage_pickup_sessions)}")
        
        active_session = None
        
        # Removed legacy transaction session check - TransactionType.PAYMENT is no longer used
        
        # If not found, check product flow sessions
        if not active_session:
            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in product_flow_sessions:
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Found product flow session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(product_flow_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in product_flow_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active product flow session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    product_flow_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = product_flow_sessions[0]
                    logger.info(f"Using most recent product flow session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        # If not found, check product pickup sessions
        if not active_session:
            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in product_pickup_sessions:
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Found product pickup session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(product_pickup_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in product_pickup_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active product pickup session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    product_pickup_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = product_pickup_sessions[0]
                    logger.info(f"Using most recent product pickup session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        # If not found, check storage flow sessions
        if not active_session:
            logger.info(f"Transaction router: Checking storage flow sessions. Variable symbol: {callback.variable_symbol}")
            logger.info(f"Transaction router: Available storage flow sessions: {[s.session_id for s in storage_flow_sessions]}")

            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in storage_flow_sessions:
                    logger.info(f"Transaction router: Comparing {session.session_id} with {callback.variable_symbol}")
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Transaction router: Found storage flow session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(storage_flow_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in storage_flow_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active storage flow session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    storage_flow_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = storage_flow_sessions[0]
                    logger.info(f"Using most recent storage flow session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        # If not found, check storage pickup sessions
        if not active_session:
            logger.info(f"Transaction router: Checking storage pickup sessions. Variable symbol: {callback.variable_symbol}")
            logger.info(f"Transaction router: Available storage pickup sessions: {[s.session_id for s in storage_pickup_sessions]}")

            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in storage_pickup_sessions:
                    logger.info(f"Transaction router: Comparing {session.session_id} with {callback.variable_symbol}")
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Transaction router: Found storage pickup session by variable_symbol (session_id): {session.session_id}")
                        break

            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(storage_pickup_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in storage_pickup_sessions
                    if ws_manager.is_connected(session.session_id)
                ]

                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active storage pickup session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")

                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    storage_pickup_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = storage_pickup_sessions[0]
                    logger.info(f"Using most recent storage pickup session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")

        if not active_session:
            raise HTTPException(
                status_code=404,
                detail="No active payment session found"
            )
        
        # Universal payment callback handling for modern session types
        if active_session.session_type in [SessionType.PRODUCT_PICKUP, SessionType.STORAGE_FLOW, SessionType.STORAGE_PICKUP]:
            session_type_name = active_session.session_type
            logger.info(f"Transaction router: Processing payment callback for {session_type_name} session: {active_session.session_id}")

            # Handle payment callback using universal payment manager
            from transaction.manager import transaction_manager

            logger.info(f"Transaction router: Calling universal payment callback handler for session {active_session.session_id}")
            result = await transaction_manager.inject_payment_callback(
                session_id=active_session.session_id,
                status=callback.status,
                message=callback.msg
            )
            logger.info(f"Transaction router: Payment callback result for {session_type_name}: {result}")

            if not result:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to process {session_type_name} payment callback"
                )

            logger.info(f"Transaction router: Successfully processed payment callback for {session_type_name} session: {active_session.session_id}")

            # Return a proper TransactionCallback
            return TransactionCallback(
                transaction_id=active_session.session_id,  # Use session_id as transaction_id
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message=f"Payment callback processed for {session_type_name}",
                data={
                    "session_type": session_type_name,
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else {"session_type": session_type_name}
            )

        # Legacy transaction handling for TRANSACTION session type
        elif active_session.session_type == SessionType.TRANSACTION:
            # Legacy transaction handling
            transaction_callback = TransactionCallback(
                transaction_id=active_session.transaction_data.get("transaction_id"),
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message=callback.msg,
                data={
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else None
            )

            logger.info(f"Processing transaction callback: {transaction_callback}")

            # Process callback
            success = await transaction_manager.handle_external_callback(transaction_callback)

            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process transaction callback"
                )

            return transaction_callback

        # Unsupported session type
        else:
            logger.error(f"Transaction router: Unsupported session type for payment callback: {active_session.session_type}")
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported session type: {active_session.session_type}"
            )

    except HTTPException:
        raise
    except Exception as err:
        logger.error(f"Error processing transaction callback: {err}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error: {str(err)}"
        )
