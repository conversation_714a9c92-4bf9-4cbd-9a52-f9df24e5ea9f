import json
import os
import asyncio
import threading
import uuid
import mysql.connector
from typing import Optional, Callable, Dict, Any
from dotenv import load_dotenv
import paho.mqtt.client as mqtt
from managers.logger_manager import logger
import ssl

# Import hardware control
from hardware.locker_control import Locker<PERSON><PERSON>roller
from hardware.electronics_api import send_command
from infrastructure.external_apis.jetveo_client import storage_change_status_async

# Import MQTT command handler
from .handler import command_handler


# Load environment variables
load_dotenv()

class MQTTClient:
    """MQTT Client for handling device communication"""

    def __init__(self):
        # MQTT Configuration from environment
        self.mqtt_enabled = os.getenv("MQTT_ENABLE", "false").lower() in ("true", "1", "yes", "on")
        self.broker = os.getenv("MQTT_HOST")
        self.port = int(os.getenv("MQTT_PORT", 1883))
        self.username = os.getenv("MQTT_USERNAME")
        self.password = os.getenv("MQTT_PASSWORD")
        self.client_id = os.getenv("MQTT_CLIENT_ID", "python-client")

        # Use CLIENT_ID for topics to match mqtt_mock.py
        CLIENT_ID = os.getenv("MQTT_CLIENT_ID", "b-003")

        self.base_topic = f"devices/{CLIENT_ID}/commands"
        self.base_response_topic = f"devices/{CLIENT_ID}/responses"

        # MQTT client instance
        self.client: Optional[mqtt.Client] = None
        self.is_connected = False
        self.is_running = False
        self.loop_thread: Optional[threading.Thread] = None

        # Message handlers
        self.message_handlers: Dict[str, Callable] = {}

        # Database connection - reuse instead of creating new connections each time
        self.conn = None
        self._setup_database_connection()

    def _setup_database_connection(self):
        """Setup database connection that will be reused"""
        try:
            self.conn = mysql.connector.connect(
                host=os.getenv("DB_HOST"),
                port=int(os.getenv("DB_PORT", 3306)),
                database=os.getenv("DB_NAME"),
                user=os.getenv("DB_USER"),
                password=os.getenv("DB_PASSWORD"),
                autocommit=True,
                pool_reset_session=True
            )
            logger.info("Database connection established for MQTT client")
        except Exception as e:
            logger.error(f"Failed to establish database connection: {e}")
            self.conn = None

    def _ensure_db_connection(self):
        """Ensure database connection is active, reconnect if needed"""
        try:
            if self.conn is None or not self.conn.is_connected():
                self._setup_database_connection()
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            self._setup_database_connection()

    # For port 8883
    def setup_client(self):
        """Initialize MQTT client with callbacks"""
        if self.client is not None:
            return None

        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_id=self.client_id)
        self.client.username_pw_set(self.username, self.password)

        # --- TLS / SSL setup ---
        if self.port == 8883:
            self.client.tls_set(cert_reqs=ssl.CERT_REQUIRED)
            self.client.tls_insecure_set(False)

        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when the client connects to the broker"""
        if rc == 0:
            self.is_connected = True
            logger.info("Connected to MQTT broker")

            # Subscribe to wildcard topic to match mqtt_mock.py
            wildcard_topic = f"{self.base_topic}/+/#"
            client.subscribe(wildcard_topic)
            logger.info(f"Subscribed to {wildcard_topic}")
        else:
            self.is_connected = False
            logger.error(f"Failed to connect to MQTT broker, return code: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when the client disconnects from the broker"""
        self.is_connected = False
        logger.info("Disconnected from MQTT broker")
        

    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received from the broker"""
        try:
            payload = json.loads(msg.payload.decode())
            topic_parts = msg.topic.split('/')

            # Use the command handler to process the message
            # Run in a new thread with its own event loop to avoid conflicts
            import threading
            import asyncio

            def run_async_handler():
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Use the command handler to process the message
                    response = loop.run_until_complete(command_handler.handle_command(topic_parts, payload))

                    # Publish the response
                    self.publish_response(msg.topic, response)

                    # Log the response topic for debugging
                    response_topic = msg.topic.replace("/commands/", "/responses/")
                    logger.info(f"Responded on {response_topic}: {response}")

                finally:
                    loop.close()

            # Run the handler in a separate thread
            handler_thread = threading.Thread(target=run_async_handler, daemon=True)
            handler_thread.start()

        except Exception as e:
            logger.error(f"Error processing message: {e}")



    def publish_response(self, command_topic: str, response: Dict[str, Any]):
        """Publish a response message to the corresponding response topic. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, skipping response publish")
            return

        if self.client and self.is_connected:
            try:
                # Convert command topic to response topic
                # devices/{clientid}/commands/{category}/{command} -> devices/{clientid}/responses/{category}/{command}
                response_topic = command_topic.replace("/commands/", "/responses/")

                self.client.publish(response_topic, json.dumps(response))
                logger.info(f"Published to {response_topic}: {response}")
            except Exception as e:
                logger.error(f"Error publishing response: {e}")
        else:
            logger.warning("Cannot publish response - client not connected")

    def publish_general_response(self, response: Dict[str, Any]):
        """Publish a response message to the general response topic. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, skipping general response publish")
            return

        if self.client and self.is_connected:
            try:
                self.client.publish(self.base_response_topic, json.dumps(response))
                logger.info(f"Published to {self.base_response_topic}: {response}")
            except Exception as e:
                logger.error(f"Error publishing response: {e}")
        else:
            logger.warning("Cannot publish response - client not connected")
    
    def add_message_handler(self, topic_pattern: str, handler: Callable):
        """Add a custom message handler for specific topics. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, skipping message handler addition")
            return
        self.message_handlers[topic_pattern] = handler

    def remove_message_handler(self, topic_pattern: str):
        """Remove a message handler. Only works if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, skipping message handler removal")
            return
        if topic_pattern in self.message_handlers:
            del self.message_handlers[topic_pattern]
    
    def start(self):
        """Start the MQTT client in a separate thread. Only starts if MQTT is enabled."""
        if not self.mqtt_enabled:
            logger.info("MQTT is disabled, client will not start")
            return

        if self.is_running:
            logger.warning("MQTT client is already running")
            return

        self.setup_client()

        if not self.broker:
            logger.error("MQTT_HOST not configured in environment")
            return

        try:
            self.client.connect(self.broker, self.port, 60)
            self.is_running = True

            # Start the network loop in a separate thread
            self.loop_thread = threading.Thread(target=self._run_loop, daemon=True)
            self.loop_thread.start()

            logger.info("MQTT client started")

        except Exception as e:
            logger.error(f"Failed to start MQTT client: {e}")
            self.is_running = False
    
    def _run_loop(self):
        """Run the MQTT network loop"""
        try:
            self.client.loop_forever()
        except Exception as e:
            logger.error(f"MQTT loop error: {e}")
        finally:
            self.is_running = False
    
    def stop(self):
        """Stop the MQTT client"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.client:
            try:
                self.client.disconnect()
                self.client.loop_stop()
            except Exception as e:
                logger.error(f"Error stopping MQTT client: {e}")
        
        if self.loop_thread and self.loop_thread.is_alive():
            self.loop_thread.join(timeout=5)
            
        logger.info("MQTT client stopped")
    
    def is_client_connected(self) -> bool:
        """Check if the client is connected. Returns False if MQTT is disabled."""
        if not self.mqtt_enabled:
            return False
        return self.is_connected and self.is_running

# Global MQTT client instance
mqtt_client = MQTTClient()

def start_mqtt_client():
    """Start the global MQTT client"""
    mqtt_client.start()

def stop_mqtt_client():
    """Stop the global MQTT client"""
    mqtt_client.stop()

def get_mqtt_client() -> MQTTClient:
    """Get the global MQTT client instance"""
    return mqtt_client


