"""
Order API Router.
Handles employment order management endpoints.
"""

from managers.logger_manager import logger
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    OrderDeliverRequest, OrderDeliverResponse,
    CustomerSendOrderRequest, CustomerSendOrderResponse,
    CheckAfterDeliveryRequest, CheckAfterDeliveryResponse,
    EmploymentSendRequest, EmploymentSendResponse,
    CustomerPickupRequest, CustomerPickupResponse,
    CustomerSendRequest, CustomerSendResponse,
    ListOrdersResponse
)
from domains.order.service import order_service
from managers.timeline_logger import log_timeline_event

router = APIRouter()

@router.post("/operator/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup_expired"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        log_timeline_event(
            event_type="pickup_expired",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_expired_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/operator/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for operator to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.operator_pickup_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        log_timeline_event(
            event_type="pickup",
            event_result="failed",
            operator_id=str(request.operator_id),
            message=f"Error in pickup_employee_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/operator/deliver", response_model=EmploymentDeliverResponse)
async def deliver_to_employee(request: EmploymentDeliverRequest):
    """
    Function to deliver orders to employees. This function is for operator.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_to_employee(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "deliver_employee"
        
        return EmploymentDeliverResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in deliver_to_employee: {e}")
        log_timeline_event(
            event_type="deliver_employee",
            event_result="failed",
            message=f"Error in deliver_to_employee: {str(e)}",
            mode="order"
        )
        return EmploymentDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_ids=None
        )

@router.post("/operator/deliver", response_model=OrderDeliverResponse)
async def operator_deliver_orders(request: OrderDeliverRequest):
    """
    Function for operator to deliver orders with status 7 or 8.
    Creates WebSocket session if there are orders to deliver.
    """
    try:
        result = await order_service.operator_deliver()

        return OrderDeliverResponse(**result)

    except Exception as e:
        logger.error(f"Error in operator_deliver_orders: {e}")
        log_timeline_event(
            event_type="operator_deliver",
            event_result="failed",
            message=f"Error in operator_deliver_orders: {str(e)}",
            mode="order"
        )
        return OrderDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            total_reservations=0
        )

@router.post("/customer/send", response_model=CustomerSendOrderResponse)
async def customer_send_orders(request: CustomerSendOrderRequest):
    """
    Function for customer to send orders with status 8.
    Creates WebSocket session if there are orders to send.
    """
    try:
        result = await order_service.customer_send_orders()

        return CustomerSendOrderResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_send_orders: {e}")
        log_timeline_event(
            event_type="customer_send",
            event_result="failed",
            message=f"Error in customer_send_orders: {str(e)}",
            mode="order"
        )
        return CustomerSendOrderResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            total_reservations=0
        )

@router.post("/operator/check-after-delivery", response_model=CheckAfterDeliveryResponse)
async def check_after_delivery(request: CheckAfterDeliveryRequest):
    """
    Check if all packages for each order were delivered.
    Complete orders where all packages have status 3, and start pickup for incomplete orders.
    """
    try:
        result = await order_service.check_after_delivery()

        return CheckAfterDeliveryResponse(**result)

    except Exception as e:
        logger.error(f"Error in check_after_delivery: {e}")
        log_timeline_event(
            event_type="check_after_delivery",
            event_result="failed",
            message=f"Error in check_after_delivery: {str(e)}",
            mode="order"
        )
        return CheckAfterDeliveryResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0,
            needs_pickup=False,
            completed_orders=0
        )

@router.post("/employment/customer/send", response_model=EmploymentSendResponse)
async def employee_send_order(request: EmploymentSendRequest):
    """
    Function for employee to send order.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.employee_send_order(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"] and result["valid"]:

            log_timeline_event(
                event_type="employee_send",
                event_result="started",
                phone_number=request.phone_number,
                message="Employee send order started",
                mode="order"
            )

            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "employee_send"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": request.phone_number,
                    "reserved_section_id": result.get("section_id")
                })
        
        elif not result["valid"]:
            log_timeline_event(
                event_type="employee_send",
                event_result="phone_number_not_found",
                phone_number=request.phone_number,
                message="Invalid phone number",
                mode="order"
            )

        return EmploymentSendResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in employee_send_order: {e}")
        log_timeline_event(
            event_type="employee_send",
            event_result="failed",
            phone_number=request.phone_number,
            message=f"Error in employee_send_order: {str(e)}",
            mode="order"
        )
        return EmploymentSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/pickup", response_model=CustomerPickupResponse)
async def customer_pickup_order(request: CustomerPickupRequest):
    """
    Function for customer to pickup order using PIN.
    Similar to product pickup - creates WebSocket session and waits for hardware_screen_ready.
    """
    try:
        result = await order_service.customer_pickup_order(request.pickup_pin)
        
        # If order is found, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_pickup"
                session.section_id = result["section_id"]  # This field exists in SessionData

            log_timeline_event(
                event_type="pin_entered",
                event_result="order_found",
                entered_pin=request.pickup_pin,
                message="Customer pickup started",
                mode="order"
            )
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="order_not_found",
                entered_pin=request.pickup_pin,
                message="Invalid pickup PIN",
                mode="order"
            )
        
        return CustomerPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in customer_pickup_order: {e}")
        log_timeline_event(
            event_type="pin_entered",
            event_result="failed",
            entered_pin=request.pickup_pin,
            message=f"Error in customer_pickup_order: {str(e)}",
            mode="order"
        )
        return CustomerPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_id=None,
            requires_payment=False,
            amount=0.0
        )



@router.get("/list-orders", response_model=ListOrdersResponse)
async def list_orders():
    """
    Get list of all orders with status 7, 8, or 3.
    Returns orders grouped by insert_pin with their packages.

    Response format:
    [
        {
            "insert_pin": "123456",
            "packages": [
                {
                    "package_pin": "123456",
                    "section_id": 1,
                    "status": 1
                }
            ]
        }
    ]
    """
    try:
        orders = order_service.get_orders_list()
        return ListOrdersResponse(orders=orders)

    except Exception as e:
        logger.error(f"Error in list_orders: {e}")
        log_timeline_event(
            event_type="list_orders",
            event_result="failed",
            message=f"Error in list_orders: {str(e)}",
            mode="order"
        )
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

