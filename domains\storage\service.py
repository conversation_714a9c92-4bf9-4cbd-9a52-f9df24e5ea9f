from typing import Dict, Any
from uuid import uuid4

from managers.session_manager import session_manager, SessionType
from infrastructure.repositories.storage_repository import StorageRepository
from managers.timeline_logger import log_timeline_event
from managers.logger_manager import logger


class StorageService:
    """Service for storage-related business operations"""

    def __init__(self):
        self.logger = logger
        self.repo = StorageRepository()

    async def insert_storage(self, section_id: int, email: str = None) -> Dict[str, Any]:
        """
        Initialize storage insertion process.
        Creates a transaction session and starts the flow.
        """
        self.logger.info(f"Storage insert requested for section_id: {section_id}, email: {email}")

        try:
            # Check if section is available for storage reservation
            availability = self.repo.check_section_availability(section_id)
            if not availability["available"]:
                self.logger.warning(f"Section {section_id} not available: {availability['reason']}")
                return {
                    "success": False,
                    "error": availability["reason"],
                    "message": f"Cannot create reservation: {availability['reason']}"
                }

            # Get category and price from the section
            category_info = self.repo.get_category_by_section(section_id)
            if not category_info:
                return {
                    "success": False,
                    "error": f"No category found for section {section_id}",
                    "message": f"Section {section_id} configuration not found"
                }

            size_category = category_info['size_category']
            price = float(category_info['price'])

            session_id = str(uuid4())

            # Create session in session manager
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.STORAGE_FLOW,
                section_id=section_id,
                amount=price,
                operation="storage_insert",
                size_category=size_category,
                email=email,
                endpoint_type="storage/insert"
            )

            if not session:
                raise Exception("Failed to create session")

            self.logger.info(f"Storage insert session created for session {session_id}, section {section_id}")

            # Note: We no longer use flow_coordinator for storage insertion
            # The new approach uses select_sections() from process_manager via websocket handler

            return {
                "success": True,
                "session_id": session_id,
                "status": "session_created",
                "requires_payment": price > 0,
                "amount": price,
                "message": "Storage insertion session created, connect to WebSocket",
                "section_id": section_id
            }

        except ValueError as e:
            return {
                "success": False,
                "error": str(e),
                "message": str(e)
            }
        except Exception as e:
            self.logger.error(f"Error in insert_storage service: {e}")
            return {
                "success": False,
                "error": "Internal server error",
                "message": f"Internal server error: {str(e)}"
            }

    async def get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.
        """
        from managers.session_manager import SectionConfig
        import mysql.connector
        from os import getenv

        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT section_id, lock_id, tempered, led_section, visible
                FROM box_sections 
                WHERE section_id = %s AND visible = 1
            """, (section_id,))
            
            config = cursor.fetchone()
            
            if not config:
                return None
                
            return SectionConfig(
                section_id=config['section_id'],
                lock_id=int(config['lock_id']) if config['lock_id'] else section_id,
                is_tempered=bool(config['tempered']),
                led_section=config['led_section'] if config['led_section'] else None
            )
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting hardware config: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def pickup_storage(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Initialize storage pickup process.
        Calculates charge based on 25-hour rule:
        - Within 25 hours: No charge (amount = 0)
        - After 25 hours: Charge 63
        """
        self.logger.info(f"Storage pickup requested for pin: {reservation_pin}")
        try:
            reservations = self.repo.find_reservations(reservation_pin=reservation_pin, status=1, limit=1)
            reservation = reservations[0] if reservations else None

            # If no active reservation found, check for recently deactivated ones
            if not reservation:
                from datetime import datetime, timedelta
                from os import getenv

                # Check for recently deactivated reservations (status=0)
                recent_reservations = self.repo.find_reservations(reservation_pin=reservation_pin, status=0, limit=10)

                # Filter by time limit (within STORAGE_MINUTES_AFTER_PICKUP minutes)
                minutes_after_pickup = int(getenv("STORAGE_MINUTES_AFTER_PICKUP", "5"))
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(minutes=minutes_after_pickup)

                recent_reservation = None
                for res in recent_reservations:
                    last_update = res.get('last_update')
                    if isinstance(last_update, str):
                        last_update = datetime.fromisoformat(last_update.replace('Z', '+00:00'))

                    if last_update and last_update >= cutoff_time:
                        # Check if no active reservation exists for this section
                        section_id = res.get('section_id')
                        active_reservations = self.repo.find_reservations(section_id=section_id, status=1, limit=1)
                        if not active_reservations:
                            recent_reservation = res
                            break

                if recent_reservation:
                    # Use the recent reservation for re-pickup
                    reservation = recent_reservation
                    self.logger.info(f"Found recent deactivated reservation for re-pickup: {reservation['uuid']}")
                else:
                    log_timeline_event(
                        event_type="pin_entered",
                        event_result="not_found",
                        message="Storage reserved Box not found",
                        entered_pin=reservation_pin,
                        mode="storage"
                    )
                    return {
                        "success": False,
                        "error": f"No reservation found with pin: {reservation_pin}",
                        "message": f"Invalid reservation PIN: {reservation_pin}"
                    }
            
            log_timeline_event(
                    event_type="pin_entered",
                    event_result="found",
                    message="Storage reserved Box not found",
                    entered_pin=reservation_pin,
                    section_id=reservation['section_id'],
                    mode="storage"
                )


            # Calculate time difference from created_at
            from datetime import datetime, timedelta
            from os import getenv

            created_at = reservation['created_at']
            current_time = datetime.now()

            # Handle both datetime and string formats
            if isinstance(created_at, str):
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

            time_diff = current_time - created_at
            hours_passed = time_diff.total_seconds() / 3600

            # Use MAX_STORAGE_HOURS from environment and reservation price for extra charges
            max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))
            price_per_cycle = float(reservation.get('price', 0) or 0)

            # Check if this is a re-pickup (status=0 reservation)
            is_re_pickup = reservation.get('status') == 0

            if is_re_pickup:
                # Re-pickup: no charge, don't deactivate reservation
                extra_cycles = 0
                extra_charge = 0.0
                storage_deactivate_reservation = False
                self.logger.info(f"Re-pickup within {minutes_after_pickup} minutes, no charge")
            else:
                # Normal pickup: calculate charges
                if hours_passed <= max_storage_hours:
                    extra_cycles = 0
                    extra_charge = 0.0
                    self.logger.info(f"Pickup within {max_storage_hours} hours ({hours_passed:.2f}h), no extra fee")
                else:
                    import math
                    extra_cycles = math.ceil((hours_passed - max_storage_hours) / max_storage_hours)
                    extra_charge = price_per_cycle * extra_cycles
                    self.logger.info(
                        f"Pickup after {max_storage_hours} hours ({hours_passed:.2f}h), "
                        f"extra cycles: {extra_cycles}, fee: {extra_charge}"
                    )
                storage_deactivate_reservation = True

            # Calculate expiration time string
            expiration_time_dt = created_at + timedelta(hours=max_storage_hours)
            storage_expiration_time_str = expiration_time_dt.strftime("%d.%m.%Y %H:%M:%S")

            session_id = str(uuid4())

            # Create simple session for storage pickup - no complex flow needed
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.STORAGE_PICKUP,  # New simple session type
                section_id=reservation['section_id'],
                amount=extra_charge,
                operation="storage_pickup",
                reservation_id=reservation['id'],
                endpoint_type="storage/pickup",
                pin=reservation_pin,  # Store the entered reservation PIN for timeline logging
                log_pin=reservation_pin,  # Store the entered reservation PIN for timeline logging
                max_storage_hours=max_storage_hours,
                storage_expiration_time_str=storage_expiration_time_str,
                storage_deactivate_reservation=storage_deactivate_reservation
            )

            if not session:
                raise Exception("Failed to create session")

            return {
                "success": True,
                "session_id": session_id,
                "status": "session_created",
                "requires_payment": extra_charge > 0,
                "amount": extra_charge,
                "message": "Storage pickup session created, connect to WebSocket",
                "section_id": reservation['section_id'],
                "hours_since_creation": round(hours_passed, 2),
                "max_storage_hours": max_storage_hours,
                "extra_charge_amount": price_per_cycle,
                "extra_cycles": extra_cycles
            }
        except ValueError as e:
            return {
                "success": False,
                "error": str(e),
                "message": str(e)
            }
        except Exception as e:
            self.logger.error(f"Error in pickup_storage service: {e}")
            return {
                "success": False,
                "error": "Internal server error",
                "message": f"Internal server error: {str(e)}"
            }


# Global service instance
storage_service = StorageService()
