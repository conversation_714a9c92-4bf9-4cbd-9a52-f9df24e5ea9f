#!/usr/bin/env python3
"""
Test script for email sender functionality
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_email_functions():
    """Test the email sender functions"""
    try:
        from infrastructure.email_sender import build_email_body, send_test_email, send_silent_test_email
        
        print("✅ Successfully imported email sender functions")
        
        # Test 1: Build HTML email body
        print("\n📧 Testing HTML email body generation...")
        html_body = build_email_body(
            pin="123456",
            locker_number="42",
            reservation_date="2024-01-15T10:30:00",
            as_html=True
        )
        
        if "123456" in html_body and "42" in html_body:
            print("✅ HTML email body generated successfully")
        else:
            print("❌ HTML email body missing expected content")
            
        # Test 2: Build plain text email body
        print("\n📧 Testing plain text email body generation...")
        text_body = build_email_body(
            pin="123456",
            locker_number="42",
            reservation_date="2024-01-15T10:30:00",
            as_html=False
        )
        
        if "PIN: 123456" in text_body and "Locker: 42" in text_body:
            print("✅ Plain text email body generated successfully")
        else:
            print("❌ Plain text email body missing expected content")
            
        print("\n📧 Email functions test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import email sender: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing email functions: {e}")
        return False

def test_storage_repository_integration():
    """Test that storage repository can import email sender"""
    try:
        from infrastructure.repositories.storage_repository import StorageRepository
        print("✅ Successfully imported StorageRepository with email sender integration")
        
        # Check if the send_test_email function is available
        from infrastructure.repositories.storage_repository import send_test_email
        print("✅ send_test_email function is available in storage repository")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import storage repository with email integration: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing storage repository integration: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Email Sender Implementation")
    print("=" * 50)
    
    # Test email functions
    email_test_passed = test_email_functions()
    
    # Test storage repository integration
    integration_test_passed = test_storage_repository_integration()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Email Functions: {'✅ PASSED' if email_test_passed else '❌ FAILED'}")
    print(f"Storage Integration: {'✅ PASSED' if integration_test_passed else '❌ FAILED'}")
    
    if email_test_passed and integration_test_passed:
        print("\n🎉 All tests passed! Email sender implementation is ready.")
        print("\n📝 Next steps:")
        print("1. Configure SMTP settings in your .env file:")
        print("   - smtp_server")
        print("   - smtp_port")
        print("   - smtp_user")
        print("   - smtp_pass")
        print("   - from_name")
        print("2. Test with actual email sending by calling edit_reservations()")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
