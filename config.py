from pydantic_settings import BaseSettings
from typing import Dict, Any
from dotenv import load_dotenv
from os import getenv

load_dotenv()

def str_to_bool(value: str) -> bool:
    return value.lower() in ('true', '1', 'yes', 'on')

def str_to_int(value: str, default: int) -> int:
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def str_to_float(value: str, default: float) -> float:
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

class DeviceConfig(BaseSettings):
    # Device type identifier
    device_type: str = getenv("DEVICE_TYPE", "default")
    
    # Feature flags - which modules are enabled
    enabled_features: Dict[str, bool] = {
        "storage": str_to_bool(getenv("FEATURE_STORAGE", "false")),
        "orders": str_to_bool(getenv("FEATURE_ORDERS", "false")),
        "sale": str_to_bool(getenv("FEATURE_SALE", "false")),
        "fsm": str_to_bool(getenv("FEATURE_FSM", "true")),
    }
    
    # Device-specific configuration
    device_settings: Dict[str, Any] = {}
    
    # FSM Hardware Configuration
    fsm_config: Dict[str, Any] = {
        # Hardware Controller
        "hardware_type": getenv("HARDWARE_TYPE", "boardctl"),
        "hardware_port": getenv("HARDWARE_PORT", "COM8"),
        "hardware_timeout": str_to_int(getenv("HARDWARE_TIMEOUT", "5"), 5),
        "hardware_version": str_to_int(getenv("HARDWARE_VERSION", "1"), 1),
        "box_virtual": str_to_bool(getenv("BOX_VIRTUAL", "false")),
        "door_state_mock_duration": str_to_int(getenv("DOOR_STATE_MOCK_DURATION", "5"), 5),
        
        # Door Timeouts (seconds)
        "door_open_timeout": str_to_int(getenv("DOOR_OPEN_TIMEOUT", "60"), 60),
        "door_close_timeout": str_to_int(getenv("DOOR_CLOSE_TIMEOUT", "30"), 30),
        
        # Separate timeouts for non-tempered lockers
        "non_tempered_close_timeout": str_to_int(getenv("NON_TEMPERED_CLOSE_TIMEOUT", "60"), 60),
        
        "polling_interval": str_to_float(getenv("POLLING_INTERVAL", "0.5"), 0.5),
        
        # Command Retry Settings
        "max_command_retries": str_to_int(getenv("MAX_COMMAND_RETRIES", "2"), 2),
        "retry_delay": str_to_int(getenv("RETRY_DELAY", "2"), 2),
        
        # LED Configuration
        "led_enabled": str_to_bool(getenv("LED_ENABLED", "true")),
        "led_colors": {
            "yellow": getenv("LED_COLORS_YELLOW", "A"),
            "green": getenv("LED_COLORS_GREEN", "B"),
            "red": getenv("LED_COLORS_RED", "C"),
        },
        
        # Debug Settings
        "debug_hardware": str_to_bool(getenv("DEBUG_HARDWARE", "false")),
        "log_level": getenv("LOG_LEVEL", "INFO"),
    }
    
    # Payment Configuration
    payment_config: Dict[str, Any] = {
        # Payment timeout in seconds
        "payment_timeout": str_to_int(getenv("PAYMENT_TIMEOUT", "160"), 160),
        "payment_service_url": getenv("PAYMENT_SERVICE_URL", "http://localhost:8080"),
        "payment_service_timeout": str_to_int(getenv("PAYMENT_SERVICE_TIMEOUT", "159"), 159),
        "payment_close_totals_hour": str_to_int(getenv("PAYMENT_CLOSE_TOTALS_HOUR", "1"), 1),
    }

# Global config instance
device_config = DeviceConfig() 