import os
import smtplib
from email.mime.text import MIMEText
from dotenv import load_dotenv
from datetime import datetime, timedelta

load_dotenv()

SMTP_SERVER = os.getenv('smtp_server')
SMTP_PORT = int(os.getenv('smtp_port', 587))
SMTP_USER = os.getenv('smtp_user')
SMTP_PASS = os.getenv('smtp_pass')
FROM_NAME = os.getenv('from_name')
FROM_EMAIL = SMTP_USER

#CC_EMAILS = ["<EMAIL>", "<EMAIL>"] 
CC_EMAILS = ["<EMAIL>"]

def build_email_body(pin=None, locker_number=None, reservation_date=None, as_html=False):
    pin = pin if pin else 'N/A'
    locker_number = locker_number if locker_number else 'N/A'
    if reservation_date:
        try:
            # Handle datetime object directly
            if isinstance(reservation_date, datetime):
                dt = reservation_date
            else:
                # Try to parse string formats
                try:
                    dt = datetime.fromisoformat(str(reservation_date))
                except Exception:
                    dt = datetime.strptime(str(reservation_date), '%d.%m.%Y %H:%M')

            valid_until = dt + timedelta(days=1)
            valid_until_str = valid_until.strftime('%Y-%m-%d %H:%M')
        except Exception:
            valid_until_str = 'N/A'
    else:
        valid_until_str = 'N/A'
    if as_html:
        current_year = datetime.now().year
        return f'''
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 0; margin: 0; min-height: 100vh;">
          <div style="max-width: 500px; margin: 0 auto; padding: 40px 20px;">
            <div style="background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
              
              <!-- Header -->
              <div style="background: linear-gradient(45deg, #667eea, #764ba2); padding: 30px; text-align: center;">
                <h1 style="color: black; margin: 0; font-size: 24px; font-weight: 600;">
                  🧳 {FROM_NAME}
                </h1>
              </div>
              
              <!-- Content -->
              <div style="padding: 40px 30px;">
                <h2 style="color: #333; margin: 0 0 30px 0; font-size: 20px; text-align: center; font-weight: 500;">
                  Reservation Details / Detail rezervace
                </h2>
                
                <!-- PIN Section -->
                <div style="background: #f8f9ff; border: 2px dashed #667eea; border-radius: 12px; padding: 30px; text-align: center; margin-bottom: 30px;">
                  <p style="margin: 0 0 10px 0; color: #666; font-size: 14px; font-weight: 500;">
                    YOUR PIN CODE / VÁŠ PIN KÓD
                  </p>
                  <div style="font-size: 48px; font-weight: bold; color: #667eea; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                    {pin}
                  </div>
                </div>
                
                <!-- Details Section -->
                <div style="background: #f9f9f9; border-radius: 12px; padding: 25px; margin-bottom: 30px;">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                    <div>
                      <p style="margin: 0; color: #888; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">
                        LOCKER / SCHRÁNKA
                      </p>
                      <p style="margin: 5px 0 0 0; color: #333; font-size: 18px; font-weight: 600;">
                        #{locker_number}
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <p style="margin: 0; color: #888; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">
                      PICKUP BY / VYZVEDNĚTE DO
                    </p>
                    <p style="margin: 5px 0 0 0; color: #333; font-size: 16px; font-weight: 500;">
                      {valid_until_str}
                    </p>
                  </div>
                </div>
                
                <!-- Instructions -->
                <div style="background: #fff3cd; border-radius: 12px; padding: 20px; border-left: 4px solid #ffc107;">
                  <h3 style="margin: 0 0 10px 0; color: #856404; font-size: 16px;">
                    📋 Instructions / Instrukce
                  </h3>
                  <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 14px; line-height: 1.6;">
                    <li><strong>EN:</strong> On the screen, click the "Pick up luggage" button, enter your PIN, and the location of your luggage will be displayed. After collecting your luggage, please close the locker.</li>
                    <li><strong>CZ:</strong> Na obrazovce klikněte na tlačítko „Vyzvednout zavazadlo", zadejte svůj PIN a zobrazí se umístění zavazadla. Po vyzvednutí prosím zavřete schránku.</li>
                  </ul>
                </div>
              </div>
              
              <!-- Footer -->
              <div style="background: #f8f9fa; padding: 20px 30px; text-align: center; border-top: 1px solid #eee;">
                <p style="margin: 0; color: #666; font-size: 12px;">
                  This is an automated message do not reply<br> Toto je automatická zpráva neodpovídejte<br>
                  Distribox by 3iD © {current_year}
                </p>
              </div>
              
            </div>
          </div>
        </div>
        '''
    else:
        return f"PIN: {pin}\nLocker: {locker_number}\nPlatnost do: {valid_until_str}"


def send_test_email(recipient_email: str, body: str = 'Test', pin=None, locker_number=None, reservation_date=None, as_html=True, cc_emails=None):
    if pin or locker_number or reservation_date:
        body = build_email_body(pin, locker_number, reservation_date, as_html=as_html)
    subtype = "html" if as_html else "plain"
    msg = MIMEText(body, subtype)
    msg['Subject'] = 'Resevation details / Detail rezervace'
    msg['From'] = f"{FROM_NAME} <{FROM_EMAIL}>"
    msg['To'] = recipient_email
    
    # Add BCC recipients if provided (recipients won't see each other)
    # Use CC_EMAILS as default if no cc_emails provided
    if cc_emails is None:
        cc_emails = CC_EMAILS
    
    if cc_emails:
        if isinstance(cc_emails, str):
            cc_emails = [cc_emails]
        all_recipients = [recipient_email] + cc_emails
    else:
        all_recipients = [recipient_email]

    with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
        server.starttls()
        server.login(SMTP_USER, SMTP_PASS)
        server.sendmail(FROM_EMAIL, all_recipients, msg.as_string()) 


def send_silent_test_email(test_emails=None, pin=None, locker_number=None, reservation_date=None):
    """
    Tichounce odešle testovací email na zadané adresy.
    Pokud dojde k chybě, nic se nestane - frontend se o tom nedozví.
    
    Args:
        test_emails: String nebo list emailových adres (výchozí: ["<EMAIL>"])
        pin: PIN kód rezervace
        locker_number: Číslo skříňky
        reservation_date: Datum rezervace
    """
    try:
        if test_emails is None:
            test_emails = ["<EMAIL>"]
        elif isinstance(test_emails, str):
            test_emails = [test_emails]
        
        body = f"""
        <div style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
            <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
                    🧳 Nová rezervace
                </h2>
                
                <div style="background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <p><strong>PIN:</strong> <span style="font-family: monospace; font-size: 18px;">{pin if pin else 'N/A'}</span></p>
                    <p><strong>Schránka:</strong> <span style="font-size: 16px;">#{locker_number if locker_number else 'N/A'}</span></p>
                    <p><strong>Datum rezervace:</strong> <span style="font-size: 16px;">{reservation_date if reservation_date else 'N/A'}</span></p>
                </div>               
            </div>
        </div>
        """
        
        msg = MIMEText(body, "html")
        msg['Subject'] = f'Rezervace, PIN: {pin if pin else "N/A"}, Locker: {locker_number if locker_number else "N/A"})'
        msg['From'] = f"{FROM_NAME} <{FROM_EMAIL}>"
        msg['To'] = ', '.join(test_emails)
        
        # Přidáme CC emaily
        all_recipients = test_emails + CC_EMAILS
        
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USER, SMTP_PASS)
            server.sendmail(FROM_EMAIL, all_recipients, msg.as_string())
            
        print(f"Testovací email úspěšně odeslán na {', '.join(test_emails)}")
        
    except Exception as e:
        print(f"Chyba při odesílání testovacího emailu: {e}")
        pass
    

    
